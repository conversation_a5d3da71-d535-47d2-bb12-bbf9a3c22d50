﻿#-------------------------------------------------------------------------------#
#        Discover additional configuration options in our documentation         #
#               https://www.jetbrains.com/help/qodana/github.html               #
#-------------------------------------------------------------------------------#

name: Qodana
on:
  workflow_dispatch:
  pull_request:
  push:
    branches:
      - main

jobs:
  qodana:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
      checks: write
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          fetch-depth: 0
      - name: '<PERSON><PERSON><PERSON> Scan'
        uses: JetBrains/qodana-action@v2025.2
        env:
          QODANA_TOKEN: ${{ secrets.QODANA_TOKEN }}
        with:
          args: --baseline,qodana.sarif.json
          # In pr-mode: 'true' Qoda<PERSON> checks only changed files
          pr-mode: false
          use-caches: true
          post-pr-comment: true
          use-annotations: true
          # Upload Qodana results (SARIF, other artifacts, logs) as an artifact to the job
          upload-result: false
          # quick-fixes available in Ultimate and Ultimate Plus plans
          push-fixes: 'none'