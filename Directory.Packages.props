<Project>
  <!-- https://learn.microsoft.com/en-us/nuget/consume-packages/central-package-management -->
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <ItemGroup>
    <!-- Avalonia packages -->
    <!-- Important: keep version in sync! -->
    <PackageVersion Include="Avalonia" Version="11.3.4" />
    <PackageVersion Include="Avalonia.Themes.Fluent" Version="11.3.4" />
    <PackageVersion Include="Avalonia.Fonts.Inter" Version="11.3.4" />
    <PackageVersion Include="Avalonia.Diagnostics" Version="11.3.4" />
    <PackageVersion Include="Avalonia.Desktop" Version="11.3.4" />
    <PackageVersion Include="Avalonia.iOS" Version="11.3.4" />
    <PackageVersion Include="Avalonia.Browser" Version="11.3.4" />
    <PackageVersion Include="Avalonia.Android" Version="11.3.4" />
    <PackageVersion Include="CommunityToolkit.Mvvm" Version="8.4.0" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection" Version="9.0.8" />
    <PackageVersion Include="Xamarin.AndroidX.Core.SplashScreen" Version="1.0.1.15" />
    <PackageVersion Include="Microsoft.Data.Sqlite.Core" Version="9.0.8" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.8" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.8" />
  </ItemGroup>
</Project>