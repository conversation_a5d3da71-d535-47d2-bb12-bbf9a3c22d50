using Microsoft.EntityFrameworkCore;
using TimeDesk.Time.Models;

namespace TimeDesk.Data;

public class AppDb : DbContext
{
    public DbSet<WorkLog> WorkLogs => Set<WorkLog>();

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        var path = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "TimeDesk", "time.db");
        optionsBuilder.UseSqlite($"Data Source={path}");
    }

    public override int SaveChanges()
    {
        Tick();
        return base.SaveChanges();
    }

    public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        Tick();
        return base.SaveChangesAsync(cancellationToken);
    }

    private void Tick()
    {
        var utcNow = DateTimeOffset.UtcNow;

        foreach (var entry in ChangeTracker.Entries<IHasTimestamps>())
        {
            if (entry.State == EntityState.Added)
            {
                entry.Entity.CreatedAt = utcNow;
                entry.Entity.UpdatedAt = utcNow;
            }
            else if (entry.State == EntityState.Modified)
            {
                entry.Entity.UpdatedAt = utcNow;
            }
        }
    }
}